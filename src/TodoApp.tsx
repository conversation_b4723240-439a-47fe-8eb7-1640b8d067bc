import { useQuery, useMutation } from "convex/react";
import AddTodoForm from "./AddTodoForm";
import TodoList from "./TodoList";
import { api } from "convex/_generated/api";
import type { Id } from "convex/_generated/dataModel";

export type Todo = {
  _id: Id<"todos">;
  text: string;
  completed: boolean;
  createdAt: string;
};

export default function TodoApp() {
  const todos = useQuery(api.todos.list) || [];
  console.log("🚀 ~ TodoApp ~ todos:", todos);
  const createTodo = useMutation(api.todos.create);
  const deleteTodo = useMutation(api.todos.remove);
  const toggleComplete = useMutation(api.todos.toggleComplete);

  const addTodo = async (text: string) => {
    try {
      await createTodo({ text });
    } catch (error) {
      console.error("Failed to add todo:", error);
    }
  };

  const toggleTodo = async (id: Id<"todos">) => {
    try {
      await toggleComplete({ id });
    } catch (error) {
      console.error("Failed to toggle todo:", error);
    }
  };

  const deleteTodoHandler = async (id: Id<"todos">) => {
    try {
      await deleteTodo({ id });
    } catch (error) {
      console.error("Failed to delete todo:", error);
    }
  };

  if (todos === undefined) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1>Todo App</h1>
      <AddTodoForm addTodo={addTodo} />
      <TodoList
        todos={todos}
        toggleTodo={toggleTodo}
        deleteTodo={deleteTodoHandler}
      />
    </div>
  );
}
