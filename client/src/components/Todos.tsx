import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";

export default function Todos() {
  const todos = useQuery(api.todos.list) || [];
  console.log("🚀 ~ Todos ~ todos:", todos);
  const createTodo = useMutation(api.todos.create);
  const updateTodo = useMutation(api.todos.update);
  const deleteTodo = useMutation(api.todos.remove);
  const toggleComplete = useMutation(api.todos.toggleComplete);

  const [newTodo, setNewTodo] = useState("");

  const handleCreateTodo = () => {
    if (newTodo.trim() === "") return;
    createTodo({ text: newTodo });
    setNewTodo("");
  };

  return (
    <div>
      <h1>Todo List</h1>
      <input
        type="text"
        value={newTodo}
        onChange={(e) => setNewTodo(e.target.value)}
      />
      <button onClick={handleCreateTodo}>Add Todo</button>
      <ul>
        {todos.map((todo) => (
          <li
            key={todo._id}
            style={{ textDecoration: todo.completed ? "line-through" : "none" }}
          >
            <span
              onClick={() =>
                updateTodo({
                  id: todo._id,
                  text: todo.text,
                  completed: !todo.completed,
                })
              }
            >
              {todo.text}
            </span>
            <button onClick={() => toggleComplete({ id: todo._id })}>
              {todo.completed ? "Undo" : "Complete"}
            </button>
            <button onClick={() => deleteTodo({ id: todo._id })}>Delete</button>
          </li>
        ))}
      </ul>
    </div>
  );
}
