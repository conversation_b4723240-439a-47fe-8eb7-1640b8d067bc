import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { ConvexProvider } from 'convex/react'
import './index.css'
import App from './App'
import convex from './convex'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      <ConvexProvider client={convex}>
        <App />
      </ConvexProvider>
    </BrowserRouter>
  </StrictMode>,
)
