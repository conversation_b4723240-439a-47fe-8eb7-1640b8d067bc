import type { Id } from "../../convex/_generated/dataModel";
import type { Todo } from "./TodoApp";
import TodoItem from "./TodoItem";

type Props = {
  todos: Todo[];
  toggleTodo: (id: Id<"todos">) => void;
  deleteTodo: (id: Id<"todos">) => void;
};

export default function TodoList({ todos, toggleTodo, deleteTodo }: Props) {
  return (
    <ul>
      {todos.map((todo) => (
        <TodoItem
          key={todo._id}
          todo={todo}
          toggleTodo={toggleTodo}
          deleteTodo={deleteTodo}
        />
      ))}
    </ul>
  );
}
