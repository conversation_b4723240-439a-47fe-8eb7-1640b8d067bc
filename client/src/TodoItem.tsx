import type { Id } from "../../convex/_generated/dataModel";
import type { Todo } from "./TodoApp";

type Props = {
  todo: Todo;
  toggleTodo: (id: Id<"todos">) => void;
  deleteTodo: (id: Id<"todos">) => void;
};

export default function TodoItem({ todo, toggleTodo, deleteTodo }: Props) {
  return (
    <li
      style={{
        textDecoration: todo.completed ? "line-through" : "none",
        cursor: "pointer",
      }}
      onClick={() => toggleTodo(todo._id)}
    >
      {todo.text}
      <button
        onClick={(e) => {
          e.stopPropagation();
          deleteTodo(todo._id);
        }}
      >
        Delete
      </button>
    </li>
  );
}
